# Novel Archives API

A backend API for managing novel/series reading progress, built with Hon<PERSON> and deployed on Cloudflare Workers.

## Features

- **Complete CRUD operations** for series management
- **Auth0 authentication** with JWT token verification
- **Multi-tenant data isolation** - users can only access their own data
- **Pagination support** with configurable page size
- **Input validation** using Zod schemas
- **Cloudflare D1 database** integration
- **TypeScript** for type safety

## API Endpoints

All endpoints require authentication via <PERSON><PERSON> token in the Authorization header.

### Series Management

- `GET /api/series` - Get paginated list of series
  - Query parameters: `page` (default: 1), `limit` (default: 10, max: 100)
- `GET /api/series/:id` - Get specific series by ID
- `POST /api/series` - Create new series
- `PUT /api/series/:id` - Update existing series
- `DELETE /api/series/:id` - Delete series

### Series Schema

```typescript
{
  id: string (UUID)
  name: string (required, unique per user)
  chapter?: number (optional, non-negative)
  status: 'Reading' | 'Completed' | 'On-Hold' | 'Dropped' | 'Cancelled' | 'Plan to Read'
  updated_at: string (ISO timestamp)
  user_id: string (from JWT subject claim)
}
```

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd novel-archives-hono-series-api
npm install
```

### 2. Configure Auth0

1. Create an Auth0 application and API
2. Note down your domain, audience, and issuer URL
3. Update `wrangler.jsonc` with your Auth0 configuration

### 3. Setup Cloudflare D1 Database

```bash
# Create a new D1 database
wrangler d1 create novel-archives-db

# Update wrangler.jsonc with the database ID returned from the command above

# Run the migration to create the series table
wrangler d1 execute novel-archives-db --file=./migrations/001_create_series_table.sql
```

### 4. Update Configuration

Edit `wrangler.jsonc` and replace the placeholder values with your actual Auth0 and database configuration. See [CONFIGURATION.md](./CONFIGURATION.md) for detailed configuration instructions including environment-specific setups.

### 5. Development

```bash
# Start development server
npm start

# Deploy to Cloudflare Workers (development)
npm run deploy

# Deploy to specific environments
wrangler deploy --env staging
wrangler deploy --env production
```

## Authentication

The API uses Auth0 for authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

The API will:
1. Verify the JWT signature using Auth0's JWKS endpoint
2. Validate the issuer and audience claims
3. Extract the user ID from the `sub` claim
4. Ensure data isolation per user

## Error Handling

The API returns appropriate HTTP status codes:

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing/invalid token)
- `404` - Not Found
- `409` - Conflict (duplicate series name)
- `500` - Internal Server Error

Error responses include a descriptive message:

```json
{
  "message": "Error description"
}
```

## Configuration

This project uses `wrangler.jsonc` for all configuration instead of `.env` files. This is the standard approach for Cloudflare Workers. See [CONFIGURATION.md](./CONFIGURATION.md) for detailed configuration instructions.

## Frontend Integration

For complete frontend integration instructions including React hooks and Auth0 setup, see the frontend integration guide at the end of this README.
