import type { Series, CreateSeriesRequest, UpdateSeriesRequest, PaginationParams, PaginatedResponse } from '../types'

export class DatabaseService {
  constructor(private db: D1Database) {}

  async createSeries(data: CreateSeriesRequest, userId: string): Promise<Series> {
    const id = crypto.randomUUID()
    const now = new Date().toISOString()

    const result = await this.db
      .prepare(`
        INSERT INTO series (id, name, chapter, status, updated_at, user_id)
        VALUES (?, ?, ?, ?, ?, ?)
        RETURNING *
      `)
      .bind(id, data.name, data.chapter || null, data.status || 'Plan to Read', now, userId)
      .first<Series>()

    if (!result) {
      throw new Error('Failed to create series')
    }

    return result
  }

  async getSeriesPaginated(userId: string, pagination: PaginationParams): Promise<PaginatedResponse<Series>> {
    const offset = (pagination.page - 1) * pagination.limit

    // Get total count
    const countResult = await this.db
      .prepare('SELECT COUNT(*) as count FROM series WHERE user_id = ?')
      .bind(userId)
      .first<{ count: number }>()

    const total = countResult?.count || 0
    const totalPages = Math.ceil(total / pagination.limit)

    // Get paginated data
    const series = await this.db
      .prepare(`
        SELECT * FROM series 
        WHERE user_id = ? 
        ORDER BY updated_at DESC 
        LIMIT ? OFFSET ?
      `)
      .bind(userId, pagination.limit, offset)
      .all<Series>()

    return {
      data: series.results || [],
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1
      }
    }
  }

  async getSeriesById(id: string, userId: string): Promise<Series | null> {
    const result = await this.db
      .prepare('SELECT * FROM series WHERE id = ? AND user_id = ?')
      .bind(id, userId)
      .first<Series>()

    return result || null
  }

  async updateSeries(id: string, data: UpdateSeriesRequest, userId: string): Promise<Series | null> {
    const existing = await this.getSeriesById(id, userId)
    if (!existing) {
      return null
    }

    const updates: string[] = []
    const values: any[] = []

    if (data.name !== undefined) {
      updates.push('name = ?')
      values.push(data.name)
    }

    if (data.chapter !== undefined) {
      updates.push('chapter = ?')
      values.push(data.chapter)
    }

    if (data.status !== undefined) {
      updates.push('status = ?')
      values.push(data.status)
    }

    if (updates.length === 0) {
      return existing // No updates to make
    }

    updates.push('updated_at = ?')
    values.push(new Date().toISOString())

    values.push(id, userId) // For WHERE clause

    const result = await this.db
      .prepare(`
        UPDATE series 
        SET ${updates.join(', ')} 
        WHERE id = ? AND user_id = ?
        RETURNING *
      `)
      .bind(...values)
      .first<Series>()

    return result || null
  }

  async deleteSeries(id: string, userId: string): Promise<boolean> {
    const result = await this.db
      .prepare('DELETE FROM series WHERE id = ? AND user_id = ? RETURNING id')
      .bind(id, userId)
      .first<{ id: string }>()

    return !!result
  }

  async checkSeriesExists(name: string, userId: string, excludeId?: string): Promise<boolean> {
    let query = 'SELECT id FROM series WHERE name = ? AND user_id = ?'
    const params = [name, userId]

    if (excludeId) {
      query += ' AND id != ?'
      params.push(excludeId)
    }

    const result = await this.db
      .prepare(query)
      .bind(...params)
      .first<{ id: string }>()

    return !!result
  }
}
