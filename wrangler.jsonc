{"$schema": "node_modules/wrangler/config-schema.json", "name": "novel-archives-api", "main": "src/index.ts", "compatibility_date": "2025-06-08", "vars": {"AUTH0_DOMAIN": "novel-archives.eu.auth0.com", "AUTH0_AUDIENCE": "https://novel-archives.eu.auth0.com/api/v2/", "JWT_ISSUER": "https://novel-archives.eu.auth0.com/"}, "d1_databases": [{"binding": "DB", "database_name": "novel-archives", "database_id": "afb9154c-9b9a-4c26-bea0-476d73077826"}], "env": {"staging": {"name": "novel-archives-api-staging", "vars": {"AUTH0_DOMAIN": "novel-archives.eu.auth0.com", "AUTH0_AUDIENCE": "https://novel-archives.eu.auth0.com/api/v2/", "JWT_ISSUER": "https://novel-archives.eu.auth0.com/"}, "d1_databases": [{"binding": "DB", "database_name": "novel-archives-staging", "database_id": "your-staging-database-id-here"}]}, "production": {"name": "novel-archives-api-production", "vars": {"AUTH0_DOMAIN": "novel-archives.eu.auth0.com", "AUTH0_AUDIENCE": "https://novel-archives.eu.auth0.com/api/v2/", "JWT_ISSUER": "https://novel-archives.eu.auth0.com/"}, "d1_databases": [{"binding": "DB", "database_name": "novel-archives-production", "database_id": "your-production-database-id-here"}]}}}