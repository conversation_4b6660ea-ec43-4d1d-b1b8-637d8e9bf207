import { z } from 'zod'

export const SeriesStatusSchema = z.enum([
  'Reading',
  'Completed', 
  'On-Hold',
  'Dropped',
  'Cancelled',
  'Plan to Read'
])

export const CreateSeriesSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters'),
  chapter: z.number().min(0, 'Chapter must be non-negative').optional(),
  status: SeriesStatusSchema.optional().default('Plan to Read')
})

export const UpdateSeriesSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters').optional(),
  chapter: z.number().min(0, 'Chapter must be non-negative').optional(),
  status: SeriesStatusSchema.optional()
})

export const PaginationSchema = z.object({
  page: z.string().optional().transform((val) => {
    const parsed = parseInt(val || '1', 10)
    return isNaN(parsed) || parsed < 1 ? 1 : parsed
  }),
  limit: z.string().optional().transform((val) => {
    const parsed = parseInt(val || '10', 10)
    return isNaN(parsed) || parsed < 1 ? 10 : Math.min(parsed, 100) // Max 100 items per page
  })
})

export const UUIDSchema = z.string().uuid('Invalid UUID format')
