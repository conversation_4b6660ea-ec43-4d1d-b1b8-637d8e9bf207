import { createMiddleware } from 'hono/factory'
import { HTTPException } from 'hono/http-exception'
import { importJWK, jwtVerify } from 'jose'
import type { Bindings, JWTPayload, AuthenticatedContext } from '../types'

interface JWK {
  kty: string
  use?: string
  key_ops?: string[]
  alg?: string
  kid?: string
  x5u?: string
  x5c?: string[]
  x5t?: string
  'x5t#S256'?: string
  [key: string]: any
}

interface JWKS {
  keys: JWK[]
}

// Cache for JWKS to avoid repeated fetches
const jwksCache = new Map<string, { jwks: JWKS; expires: number }>()

async function getJWKS(domain: string): Promise<JWKS> {
  const cacheKey = domain
  const cached = jwksCache.get(cacheKey)
  
  if (cached && cached.expires > Date.now()) {
    return cached.jwks
  }

  const response = await fetch(`https://${domain}/.well-known/jwks.json`)
  if (!response.ok) {
    throw new Error('Failed to fetch JWKS')
  }

  const jwks: JWKS = await response.json()
  
  // Cache for 1 hour
  jwksCache.set(cacheKey, {
    jwks,
    expires: Date.now() + 60 * 60 * 1000
  })

  return jwks
}

async function verifyJWT(token: string, domain: string, audience: string, issuer: string): Promise<JWTPayload> {
  try {
    const jwks = await getJWKS(domain)
    
    // Decode the token header to get the kid
    const [headerB64] = token.split('.')
    const header = JSON.parse(atob(headerB64))
    
    if (!header.kid) {
      throw new Error('Token missing kid in header')
    }

    // Find the matching key
    const jwk = jwks.keys.find(key => key.kid === header.kid)
    if (!jwk) {
      throw new Error('Unable to find matching key')
    }

    // Import the JWK
    const key = await importJWK(jwk)

    // Verify the JWT
    const { payload } = await jwtVerify(token, key, {
      issuer,
      audience
    })

    return payload as JWTPayload
  } catch (error) {
    throw new Error(`JWT verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export const authMiddleware = createMiddleware<{
  Bindings: Bindings
  Variables: AuthenticatedContext
}>(async (c, next) => {
  const authHeader = c.req.header('Authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new HTTPException(401, { message: 'Missing or invalid authorization header' })
  }

  const token = authHeader.substring(7) // Remove 'Bearer ' prefix

  try {
    const payload = await verifyJWT(
      token,
      c.env.AUTH0_DOMAIN,
      c.env.AUTH0_AUDIENCE,
      c.env.JWT_ISSUER
    )

    if (!payload.sub) {
      throw new Error('Token missing subject claim')
    }

    // Set user context
    c.set('user', { id: payload.sub })

    await next()
  } catch (error) {
    throw new HTTPException(401, { 
      message: `Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
    })
  }
})
