-- Migration: Create series table
-- This migration creates the series table with multi-tenant support

CREATE TABLE IF NOT EXISTS series (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
    name TEXT NOT NULL,
    chapter REAL,
    status TEXT CHECK (status IN ('Reading', 'Completed', 'On-Hold', 'Dropped', 'Cancelled', 'Plan to Read')) DEFAULT 'Plan to Read',
    updated_at TEXT DEFAULT (datetime('now')),
    user_id TEXT NOT NULL
);

-- Create unique constraint on name per user (multi-tenant)
CREATE UNIQUE INDEX IF NOT EXISTS idx_series_name_user ON series(name, user_id);

-- Create index on user_id for efficient queries
CREATE INDEX IF NOT EXISTS idx_series_user_id ON series(user_id);

-- Create index on updated_at for sorting
CREATE INDEX IF NOT EXISTS idx_series_updated_at ON series(updated_at DESC);
