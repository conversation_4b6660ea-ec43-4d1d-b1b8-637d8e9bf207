import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { authMiddleware } from './middleware/auth'
import { seriesRoutes } from './routes/series'
import type { Bindings } from './types'

const app = new Hono<{ Bindings: Bindings }>()

// Global middleware
app.use('*', logger())
app.use('*', cors({
  origin: ['http://localhost:3000', 'https://your-frontend-domain.com'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}))

// Health check endpoint
app.get('/', (c) => {
  return c.json({ message: 'Novel Archives API is running', version: '1.0.0' })
})

// Protected routes
app.use('/api/series/*', authMiddleware)
app.route('/api/series', seriesRoutes)

export default app
