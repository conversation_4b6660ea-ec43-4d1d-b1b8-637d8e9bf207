import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { seriesRoutes } from "./routes/series";
import type { Bindings } from "./types";

const app = new Hono<{ Bindings: Bindings }>();

// Global middleware
app.use("*", logger());
app.use(
  "*",
  cors({
    origin: [
      "http://localhost:3000",
      "http://localhost:5173",
      "http://127.0.0.1:5173",
      "https://your-frontend-domain.com",
    ],
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"],
  })
);

// Health check endpoint
app.get("/", (c) => {
  return c.json({ message: "Novel Archives API is running", version: "1.0.0" });
});

// Protected routes with scope-based authorization
app.route("/api/series", seriesRoutes);

export default app;
