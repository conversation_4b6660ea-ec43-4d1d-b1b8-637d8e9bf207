export interface Bindings {
  DB: D1Database
  AUTH0_DOMAIN: string
  AUTH0_AUDIENCE: string
  JWT_ISSUER: string
}

export interface Series {
  id: string
  name: string
  chapter: number | null
  status: SeriesStatus
  updated_at: string
  user_id: string
}

export type SeriesStatus = 
  | 'Reading' 
  | 'Completed' 
  | 'On-Hold' 
  | 'Dropped' 
  | 'Cancelled' 
  | 'Plan to Read'

export interface CreateSeriesRequest {
  name: string
  chapter?: number
  status?: SeriesStatus
}

export interface UpdateSeriesRequest {
  name?: string
  chapter?: number
  status?: SeriesStatus
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface JWTPayload {
  sub: string
  iss: string
  aud: string | string[]
  exp: number
  iat: number
  [key: string]: any
}

export interface AuthenticatedContext {
  user: {
    id: string
  }
}
