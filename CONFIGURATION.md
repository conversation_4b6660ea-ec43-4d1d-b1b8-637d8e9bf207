# Configuration Guide

This project uses `wrangler.jsonc` for all configuration instead of `.env` files. This is the recommended approach for Cloudflare Workers.

## Basic Configuration

The main configuration is in `wrangler.jsonc`:

```jsonc
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "novel-archives-api",
  "main": "src/index.ts",
  "compatibility_date": "2025-06-08",
  "vars": {
    "AUTH0_DOMAIN": "your-auth0-domain.auth0.com",
    "AUTH0_AUDIENCE": "your-api-identifier",
    "JWT_ISSUER": "https://your-auth0-domain.auth0.com/"
  },
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "novel-archives",
      "database_id": "your-database-id"
    }
  ]
}
```

## Environment Variables

All environment variables are defined in the `vars` section:

- **AUTH0_DOMAIN**: Your Auth0 domain (e.g., `your-tenant.auth0.com`)
- **AUTH0_AUDIENCE**: Your API identifier from Auth0
- **JWT_ISSUER**: The issuer URL (usually `https://your-domain.auth0.com/`)

## Database Configuration

The D1 database is configured in the `d1_databases` section:

- **binding**: The variable name used in your code (`DB`)
- **database_name**: The name of your D1 database
- **database_id**: The unique ID of your D1 database

## Environment-Specific Configuration

You can define different configurations for different environments:

```jsonc
{
  // ... base configuration for development
  "env": {
    "staging": {
      "name": "novel-archives-api-staging",
      "vars": {
        "AUTH0_DOMAIN": "staging-domain.auth0.com",
        "AUTH0_AUDIENCE": "staging-api-identifier",
        "JWT_ISSUER": "https://staging-domain.auth0.com/"
      },
      "d1_databases": [
        {
          "binding": "DB",
          "database_name": "novel-archives-staging",
          "database_id": "staging-database-id"
        }
      ]
    },
    "production": {
      "name": "novel-archives-api-production",
      "vars": {
        "AUTH0_DOMAIN": "production-domain.auth0.com",
        "AUTH0_AUDIENCE": "production-api-identifier",
        "JWT_ISSUER": "https://production-domain.auth0.com/"
      },
      "d1_databases": [
        {
          "binding": "DB",
          "database_name": "novel-archives-production",
          "database_id": "production-database-id"
        }
      ]
    }
  }
}
```

## Deployment Commands

Deploy to different environments:

```bash
# Deploy to development (default)
wrangler deploy

# Deploy to staging
wrangler deploy --env staging

# Deploy to production
wrangler deploy --env production
```

## Database Setup for Each Environment

Create databases for each environment:

```bash
# Development database (already created)
wrangler d1 create novel-archives

# Staging database
wrangler d1 create novel-archives-staging

# Production database
wrangler d1 create novel-archives-production
```

Run migrations for each environment:

```bash
# Development
wrangler d1 execute novel-archives --file=./migrations/001_create_series_table.sql

# Staging
wrangler d1 execute novel-archives-staging --file=./migrations/001_create_series_table.sql

# Production
wrangler d1 execute novel-archives-production --file=./migrations/001_create_series_table.sql
```

## Security Best Practices

1. **Never commit sensitive values** to version control
2. **Use different Auth0 tenants** for different environments
3. **Use separate databases** for each environment
4. **Rotate secrets regularly**

## Local Development

For local development, wrangler automatically uses the configuration from the base level of `wrangler.jsonc`. The local D1 database will be automatically created and managed by wrangler.

## Accessing Environment Variables in Code

Environment variables are accessed through the `c.env` object in your Hono handlers:

```typescript
// In your middleware or handlers
const authDomain = c.env.AUTH0_DOMAIN;
const audience = c.env.AUTH0_AUDIENCE;
const issuer = c.env.JWT_ISSUER;
const db = c.env.DB; // D1 database instance
```

## Troubleshooting

### Common Issues

1. **Database ID not found**: Make sure you've updated the `database_id` in `wrangler.jsonc` with the actual ID returned from `wrangler d1 create`

2. **Auth0 configuration errors**: Verify that your Auth0 domain, audience, and issuer are correctly configured and match your Auth0 setup

3. **Environment not found**: Make sure you're using the correct environment name when deploying with `--env`

### Checking Configuration

You can verify your configuration with:

```bash
# Check current configuration
wrangler whoami

# List your D1 databases
wrangler d1 list

# Check environment-specific configuration
wrangler deploy --env staging --dry-run
```
