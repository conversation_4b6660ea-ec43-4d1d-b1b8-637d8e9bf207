# Configuration Guide

This project uses `wrangler.jsonc` for all configuration instead of `.env` files. This is the recommended approach for Cloudflare Workers.

## Single Database Configuration

This project uses a single production database for both development and production environments. The configuration is in `wrangler.jsonc`:

```jsonc
{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "novel-archives-api",
  "main": "src/index.ts",
  "compatibility_date": "2025-06-08",
  "vars": {
    "AUTH0_DOMAIN": "novel-archives.eu.auth0.com",
    "AUTH0_AUDIENCE": "https://novel-archives.eu.auth0.com/api/v2/",
    "JWT_ISSUER": "https://novel-archives.eu.auth0.com/"
  },
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "novel-archives",
      "database_id": "afb9154c-9b9a-4c26-bea0-476d73077826"
    }
  ]
}
```

## Environment Variables

All environment variables are defined in the `vars` section:

- **AUTH0_DOMAIN**: Your Auth0 domain (e.g., `novel-archives.eu.auth0.com`)
- **AUTH0_AUDIENCE**: Your API identifier from Auth0
- **JWT_ISSUER**: The issuer URL (usually `https://your-domain.auth0.com/`)

## Database Configuration

The D1 database is configured in the `d1_databases` section:

- **binding**: The variable name used in your code (`DB`)
- **database_name**: The name of your D1 database (`novel-archives`)
- **database_id**: The unique ID of your D1 database

## Deployment

Since we use a single production database, deployment is straightforward:

```bash
# Deploy to production
npm run deploy
# or
wrangler deploy
```

## Database Setup

The database is already created and configured. If you need to run migrations:

```bash
# Run migrations on the production database
wrangler d1 execute novel-archives --file=./migrations/001_create_series_table.sql
```

## Security Best Practices

1. **Never commit sensitive values** to version control
2. **Use multi-tenant data isolation** - users can only access their own data
3. **Rotate secrets regularly**
4. **Monitor database access** through Cloudflare dashboard

## Local Development

For local development, wrangler automatically uses the configuration from `wrangler.jsonc`. The local D1 database will be automatically created and managed by wrangler, but it connects to the same production database for consistency.

## Accessing Environment Variables in Code

Environment variables are accessed through the `c.env` object in your Hono handlers:

```typescript
// In your middleware or handlers
const authDomain = c.env.AUTH0_DOMAIN;
const audience = c.env.AUTH0_AUDIENCE;
const issuer = c.env.JWT_ISSUER;
const db = c.env.DB; // D1 database instance
```

## Troubleshooting

### Common Issues

1. **Database ID not found**: Make sure you've updated the `database_id` in `wrangler.jsonc` with the actual ID returned from `wrangler d1 create`

2. **Auth0 configuration errors**: Verify that your Auth0 domain, audience, and issuer are correctly configured and match your Auth0 setup

3. **Environment not found**: Make sure you're using the correct environment name when deploying with `--env`

### Checking Configuration

You can verify your configuration with:

```bash
# Check current configuration
wrangler whoami

# List your D1 databases
wrangler d1 list

# Check deployment configuration
wrangler deploy --dry-run
```
