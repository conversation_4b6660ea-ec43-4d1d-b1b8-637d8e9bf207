# API Usage Examples

## Authentication

All requests require a valid JWT token from Auth<PERSON> in the Authorization header:

```bash
Authorization: <PERSON><PERSON> eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6...
```

## Base URL

- Development: `http://127.0.0.1:8787`
- Production: `https://your-worker-domain.workers.dev`

## Examples

### 1. Health Check

```bash
curl -X GET "http://127.0.0.1:8787/"
```

Response:
```json
{
  "message": "Novel Archives API is running",
  "version": "1.0.0"
}
```

### 2. Create a New Series

```bash
curl -X POST "http://127.0.0.1:8787/api/series" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "One Piece",
    "chapter": 1095,
    "status": "Reading"
  }'
```

Response:
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "One Piece",
  "chapter": 1095,
  "status": "Reading",
  "updated_at": "2024-01-15T10:30:00.000Z",
  "user_id": "auth0|user123"
}
```

### 3. Get All Series (Paginated)

```bash
curl -X GET "http://127.0.0.1:8787/api/series?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "One Piece",
      "chapter": 1095,
      "status": "Reading",
      "updated_at": "2024-01-15T10:30:00.000Z",
      "user_id": "auth0|user123"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

### 4. Get Specific Series

```bash
curl -X GET "http://127.0.0.1:8787/api/series/123e4567-e89b-12d3-a456-426614174000" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "One Piece",
  "chapter": 1095,
  "status": "Reading",
  "updated_at": "2024-01-15T10:30:00.000Z",
  "user_id": "auth0|user123"
}
```

### 5. Update Series

```bash
curl -X PUT "http://127.0.0.1:8787/api/series/123e4567-e89b-12d3-a456-426614174000" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "chapter": 1096,
    "status": "Reading"
  }'
```

Response:
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "One Piece",
  "chapter": 1096,
  "status": "Reading",
  "updated_at": "2024-01-15T11:00:00.000Z",
  "user_id": "auth0|user123"
}
```

### 6. Delete Series

```bash
curl -X DELETE "http://127.0.0.1:8787/api/series/123e4567-e89b-12d3-a456-426614174000" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
{
  "message": "Series deleted successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "message": "Validation failed",
  "cause": [
    {
      "code": "too_small",
      "minimum": 1,
      "type": "string",
      "inclusive": true,
      "exact": false,
      "message": "Name is required",
      "path": ["name"]
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "message": "Authentication failed: JWT verification failed: Token missing kid in header"
}
```

### 404 Not Found
```json
{
  "message": "Series not found"
}
```

### 409 Conflict
```json
{
  "message": "Series with this name already exists"
}
```

## Status Values

Valid status values for series:
- `Reading`
- `Completed`
- `On-Hold`
- `Dropped`
- `Cancelled`
- `Plan to Read` (default)
