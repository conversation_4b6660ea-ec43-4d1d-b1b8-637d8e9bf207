import { Hono } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { validator } from 'hono/validator'
import { DatabaseService } from '../services/database'
import { CreateSeriesSchema, UpdateSeriesSchema, PaginationSchema, UUIDSchema } from '../schemas'
import type { Bindings, AuthenticatedContext } from '../types'

export const seriesRoutes = new Hono<{
  Bindings: Bindings
  Variables: AuthenticatedContext
}>()

// GET /api/series - Get all series with pagination
seriesRoutes.get(
  '/',
  validator('query', (value, c) => {
    const result = PaginationSchema.safeParse(value)
    if (!result.success) {
      throw new HTTPException(400, { message: 'Invalid pagination parameters' })
    }
    return result.data
  }),
  async (c) => {
    const { page, limit } = c.req.valid('query')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      const result = await db.getSeriesPaginated(user.id, { page, limit })
      return c.json(result)
    } catch (error) {
      throw new HTTPException(500, { 
        message: `Failed to fetch series: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }
  }
)

// GET /api/series/:id - Get a specific series
seriesRoutes.get(
  '/:id',
  validator('param', (value, c) => {
    const result = UUIDSchema.safeParse(value.id)
    if (!result.success) {
      throw new HTTPException(400, { message: 'Invalid series ID format' })
    }
    return { id: result.data }
  }),
  async (c) => {
    const { id } = c.req.valid('param')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      const series = await db.getSeriesById(id, user.id)
      
      if (!series) {
        throw new HTTPException(404, { message: 'Series not found' })
      }

      return c.json(series)
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      throw new HTTPException(500, { 
        message: `Failed to fetch series: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }
  }
)

// POST /api/series - Create a new series
seriesRoutes.post(
  '/',
  validator('json', (value, c) => {
    const result = CreateSeriesSchema.safeParse(value)
    if (!result.success) {
      throw new HTTPException(400, { 
        message: 'Validation failed', 
        cause: result.error.errors 
      })
    }
    return result.data
  }),
  async (c) => {
    const data = c.req.valid('json')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      // Check if series with same name already exists for this user
      const exists = await db.checkSeriesExists(data.name, user.id)
      if (exists) {
        throw new HTTPException(409, { message: 'Series with this name already exists' })
      }

      const series = await db.createSeries(data, user.id)
      return c.json(series, 201)
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      throw new HTTPException(500, { 
        message: `Failed to create series: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }
  }
)

// PUT /api/series/:id - Update a series
seriesRoutes.put(
  '/:id',
  validator('param', (value, c) => {
    const result = UUIDSchema.safeParse(value.id)
    if (!result.success) {
      throw new HTTPException(400, { message: 'Invalid series ID format' })
    }
    return { id: result.data }
  }),
  validator('json', (value, c) => {
    const result = UpdateSeriesSchema.safeParse(value)
    if (!result.success) {
      throw new HTTPException(400, { 
        message: 'Validation failed', 
        cause: result.error.errors 
      })
    }
    return result.data
  }),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      // Check if updating name would create a duplicate
      if (data.name) {
        const exists = await db.checkSeriesExists(data.name, user.id, id)
        if (exists) {
          throw new HTTPException(409, { message: 'Series with this name already exists' })
        }
      }

      const series = await db.updateSeries(id, data, user.id)
      
      if (!series) {
        throw new HTTPException(404, { message: 'Series not found' })
      }

      return c.json(series)
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      throw new HTTPException(500, { 
        message: `Failed to update series: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }
  }
)

// DELETE /api/series/:id - Delete a series
seriesRoutes.delete(
  '/:id',
  validator('param', (value, c) => {
    const result = UUIDSchema.safeParse(value.id)
    if (!result.success) {
      throw new HTTPException(400, { message: 'Invalid series ID format' })
    }
    return { id: result.data }
  }),
  async (c) => {
    const { id } = c.req.valid('param')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      const deleted = await db.deleteSeries(id, user.id)
      
      if (!deleted) {
        throw new HTTPException(404, { message: 'Series not found' })
      }

      return c.json({ message: 'Series deleted successfully' })
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error
      }
      throw new HTTPException(500, { 
        message: `Failed to delete series: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    }
  }
)
