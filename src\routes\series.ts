import { Hono } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { validator } from 'hono/validator'
import { DatabaseService } from '../services/database'
import { CreateSeriesSchema, UpdateSeriesSchema, PaginationSchema, UUIDSchema } from '../schemas'
import { authMiddleware } from '../middleware/auth'
import type { Bindings, AuthenticatedContext } from '../types'

export const seriesRoutes = new Hono<{
  Bindings: Bindings
  Variables: AuthenticatedContext
}>()

// GET /api/series - Get all series with pagination
seriesRoutes.get(
  '/',
  authMiddleware('read:series'),
  validator('query', (value, c) => {
    const result = PaginationSchema.safeParse(value)
    if (!result.success) {
      return c.json({
        error: 'Validation Error',
        message: 'Invalid query parameters',
        details: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      }, 400)
    }
    return result.data
  }),
  async (c) => {
    const pagination = c.req.valid('query')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      const result = await db.getSeriesPaginated(user.id, pagination)
      return c.json({
        success: true,
        data: result
      })
    } catch (error) {
      return c.json({
        error: 'Server Error',
        message: 'Failed to fetch series',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

// GET /api/series/:id - Get a specific series
seriesRoutes.get(
  '/:id',
  authMiddleware('read:series'),
  validator('param', (value, c) => {
    const result = UUIDSchema.safeParse(value.id)
    if (!result.success) {
      return c.json({
        error: 'Validation Error',
        message: 'Invalid series ID format'
      }, 400)
    }
    return { id: result.data }
  }),
  async (c) => {
    const { id } = c.req.valid('param')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      const series = await db.getSeriesById(id, user.id)

      if (!series) {
        return c.json({
          error: 'Not Found',
          message: 'Series not found'
        }, 404)
      }

      return c.json(series)
    } catch (error) {
      return c.json({
        error: 'Server Error',
        message: 'Failed to fetch series',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

// POST /api/series - Create a new series
seriesRoutes.post(
  '/',
  authMiddleware('write:series'),
  validator('json', (value, c) => {
    const result = CreateSeriesSchema.safeParse(value)
    if (!result.success) {
      return c.json({
        error: 'Validation Error',
        message: 'Invalid request data',
        details: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      }, 400)
    }
    return result.data
  }),
  async (c) => {
    const data = c.req.valid('json')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      // Check if series with same name already exists for this user
      const existingSeries = await db.checkSeriesExists(data.name, user.id)
      if (existingSeries) {
        return c.json({
          error: 'Duplicate series',
          message: 'A series with this name already exists',
          existingSeries,
          proposedData: data
        }, 409)
      }

      const series = await db.createSeries(data, user.id)
      return c.json(series, 201)
    } catch (error) {
      return c.json({
        error: 'Server Error',
        message: 'Failed to create series',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

// PUT /api/series/:id - Update a series
seriesRoutes.put(
  '/:id',
  authMiddleware('write:series'),
  validator('param', (value, c) => {
    const result = UUIDSchema.safeParse(value.id)
    if (!result.success) {
      return c.json({
        error: 'Validation Error',
        message: 'Invalid series ID format'
      }, 400)
    }
    return { id: result.data }
  }),
  validator('json', (value, c) => {
    const result = UpdateSeriesSchema.safeParse(value)
    if (!result.success) {
      return c.json({
        error: 'Validation Error',
        message: 'Invalid request data',
        details: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      }, 400)
    }
    return result.data
  }),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      // Check if updating name would create a duplicate
      if (data.name) {
        const existingSeries = await db.checkSeriesExists(data.name, user.id, id)
        if (existingSeries) {
          return c.json({
            error: 'Duplicate series',
            message: 'A series with this name already exists',
            existingSeries,
            proposedData: data
          }, 409)
        }
      }

      const series = await db.updateSeries(id, data, user.id)

      if (!series) {
        return c.json({
          error: 'Not Found',
          message: 'Series not found'
        }, 404)
      }

      return c.json(series)
    } catch (error) {
      return c.json({
        error: 'Server Error',
        message: 'Failed to update series',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

// DELETE /api/series/:id - Delete a series
seriesRoutes.delete(
  '/:id',
  authMiddleware('write:series'),
  validator('param', (value, c) => {
    const result = UUIDSchema.safeParse(value.id)
    if (!result.success) {
      return c.json({
        error: 'Validation Error',
        message: 'Invalid series ID format'
      }, 400)
    }
    return { id: result.data }
  }),
  async (c) => {
    const { id } = c.req.valid('param')
    const user = c.get('user')
    const db = new DatabaseService(c.env.DB)

    try {
      const deleted = await db.deleteSeries(id, user.id)

      if (!deleted) {
        return c.json({
          error: 'Not Found',
          message: 'Series not found'
        }, 404)
      }

      return c.body(null, 204) // 204 No Content
    } catch (error) {
      return c.json({
        error: 'Server Error',
        message: 'Failed to delete series',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 500)
    }
  }
)

// GET /api/series/statuses - Get available status options
seriesRoutes.get('/statuses', authMiddleware('read:series'), async (c) => {
  const statuses = [
    'Reading',
    'Completed',
    'On-Hold',
    'Dropped',
    'Cancelled',
    'Plan to Read'
  ]

  return c.json(statuses)
})
